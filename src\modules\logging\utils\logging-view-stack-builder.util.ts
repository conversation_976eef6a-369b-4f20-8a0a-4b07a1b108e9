import { uniqueId, isEmpty } from "lodash";
import { resizeLoggingPointData } from "@/modules/image/helpers/image.helpers";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { LoggingViewStack } from "../model/dtos/logging.config";
import { EnumLoggingViewStack, EnumLoggingExtraViews } from "../model/enum/logging.enum";
import {
  createImageStack,
  createHyperspectralOverlayStack,
  createHyperspectralBelowStack,
  findIntersectingLoggingBars,
  calculateLoggingBarDimensions,
  findRockGroupColor,
} from "./logging.utils";
import {
  EXTRA_SPACING_FOR_LOGGING_BARS,
  IMAGE_LOGGING_BAR_SPACING,
  LOGGING_BAR_HEIGHT,
} from "../constants/logging.constants";

/**
 * Builder class for constructing logging view stacks with proper positioning
 * Implements the Builder pattern to create different types of stacks systematically
 */
export class LoggingViewStackBuilder {
  private stacks: LoggingViewStack[] = [];
  private previousStartY: number = 200;
  private previousHeight: number = 0;

  constructor(
    private readonly imageRows: any[],
    private readonly imageGap: number,
    private readonly imageExtraRows: any[],
    private readonly extraViews: EnumLoggingExtraViews[],
    private readonly loggingBarData: any[],
    private readonly selectedImageSubTypeId: string,
    private readonly rockGroupFieldIds: string[],
    private readonly filteredDataEntry: any[],
    private readonly selectedAttribute: string[],
    private readonly dataDownholes: any[]
  ) { }

  /**
   * Main method to build all stacks for all image rows
   */
  public build(): LoggingViewStack[] {
    this.stacks = [];
    this.previousStartY = 200;
    this.previousHeight = 0;

    for (let i = 0; i < this.imageRows.length; i++) {
      this.buildStacksForImageRow(i);
    }

    return this.stacks;
  }

  /**
   * Build all stacks for a single image row
   */
  private buildStacksForImageRow(imageIndex: number): void {
    const currentImageRow = this.imageRows[imageIndex];
    const lastEndY = this.previousStartY + this.previousHeight;

    // Build standard image stack
    const imageStack = this.buildStandardImageStack(currentImageRow, imageIndex, lastEndY);
    let totalRowHeight = imageStack.data.height;
    this.stacks.push(imageStack);

    // Build extra row images (overlay and below)
    totalRowHeight += this.buildExtraRowImages(currentImageRow, imageIndex, lastEndY, imageStack);

    // Build logging bars
    totalRowHeight += this.buildLoggingBars(currentImageRow, imageIndex, lastEndY, totalRowHeight);

    // Build geophysical points
    totalRowHeight += this.buildGeophysicalPoints(currentImageRow, imageIndex, lastEndY, totalRowHeight);

    // Update position tracking for next image row
    this.previousStartY = imageStack.startY;
    this.previousHeight = totalRowHeight + EXTRA_SPACING_FOR_LOGGING_BARS;
  }

  /**
   * Build standard image stack
   */
  private buildStandardImageStack(
    currentImageRow: any,
    imageIndex: number,
    lastEndY: number
  ): LoggingViewStack {
    return createImageStack(
      currentImageRow,
      imageIndex,
      lastEndY,
      this.imageGap,
      this.imageRows
    );
  }

  /**
   * Build extra row images (overlay and below types)
   */
  private buildExtraRowImages(
    currentImageRow: any,
    imageIndex: number,
    lastEndY: number,
    imageStack: LoggingViewStack
  ): number {
    let additionalHeight = 0;

    // Add hyperspectral overlay stack
    if (this.shouldShowOverlay(imageIndex)) {
      const overlayStack = createHyperspectralOverlayStack(
        this.imageExtraRows[imageIndex],
        currentImageRow,
        imageIndex,
        lastEndY,
        this.imageGap
      );
      this.stacks.push(overlayStack);
    }

    // Add hyperspectral below stack
    if (this.shouldShowBelow(imageIndex)) {
      const belowStack = createHyperspectralBelowStack(
        this.imageExtraRows[imageIndex],
        currentImageRow,
        imageIndex,
        lastEndY + imageStack.data.height + this.imageGap
      );
      additionalHeight += belowStack.data.height;
      this.stacks.push(belowStack);
    }

    return additionalHeight;
  }

  /**
   * Build logging bar stacks
   */
  private buildLoggingBars(
    currentImageRow: any,
    imageIndex: number,
    lastEndY: number,
    totalRowHeight: number
  ): number {
    if (!this.shouldShowLoggingBars()) {
      return 0;
    }

    const currentImageCropId = currentImageRow.id;
    const intersectingLoggingBars = findIntersectingLoggingBars(
      this.loggingBarData,
      currentImageCropId
    );

    if (intersectingLoggingBars.length === 0) {
      return 0;
    }

    let additionalHeight = 0;

    this.rockGroupFieldIds.forEach((rockGroupId, rockGroupIndex) => {
      const firstLoggingSpacing = rockGroupIndex === 0 ? IMAGE_LOGGING_BAR_SPACING : 0;

      intersectingLoggingBars.forEach((loggingBar: any) => {
        const loggingBarStack = this.createLoggingBarStack(
          loggingBar,
          currentImageRow,
          rockGroupId,
          rockGroupIndex,
          lastEndY,
          totalRowHeight,
          firstLoggingSpacing
        );
        this.stacks.push(loggingBarStack);
      });

      additionalHeight += LOGGING_BAR_HEIGHT + IMAGE_LOGGING_BAR_SPACING + firstLoggingSpacing;
    });

    return additionalHeight;
  }

  /**
   * Create a single logging bar stack
   */
  private createLoggingBarStack(
    loggingBar: any,
    currentImageRow: any,
    rockGroupId: string,
    rockGroupIndex: number,
    lastEndY: number,
    totalRowHeight: number,
    firstLoggingSpacing: number
  ): LoggingViewStack {
    const imageWidth = currentImageRow.coordinate.Width;
    const { xPosition, width } = calculateLoggingBarDimensions(
      loggingBar,
      currentImageRow.id,
      imageWidth
    );

    const rockGroupColor = findRockGroupColor(
      loggingBar,
      rockGroupId,
    );

    return {
      id: `${currentImageRow.id}-${EnumLoggingViewStack.LoggingBar}-${loggingBar.id}-${rockGroupIndex}`,
      data: {
        width,
        height: LOGGING_BAR_HEIGHT,
        depthFrom: loggingBar.depthFrom,
        depthTo: loggingBar.depthTo,
        x: xPosition,
        color: rockGroupColor,
        loggingBarId: loggingBar.id,
        dataEntryId: loggingBar?.dataEntryId,
        startImageCropId: loggingBar.startImageCropId,
        endImageCropId: loggingBar.endImageCropId,
        betweenImageCropIds: loggingBar.betweenImageCropIds,
      },
      startY: lastEndY + this.imageGap + totalRowHeight + firstLoggingSpacing,
      type: EnumLoggingViewStack.LoggingBar,
      index: rockGroupIndex,
    };
  }

  /**
   * Build geophysical point stacks
   */
  private buildGeophysicalPoints(
    currentImageRow: any,
    imageIndex: number,
    lastEndY: number,
    totalRowHeight: number
  ): number {
    if (!this.selectedAttribute?.length) {
      return 0;
    }

    let additionalHeight = 0;
    const pointHeight = 120;
    const pointGap = 120;

    const pointStacks = this.selectedAttribute
      .map((geophysicName, index) => {
        const stack = this.createGeophysicalPointStack(
          geophysicName,
          index,
          currentImageRow,
          imageIndex,
          lastEndY,
          totalRowHeight + additionalHeight,
          pointHeight,
          pointGap
        );

        if (stack) {
          additionalHeight += pointHeight + pointGap;
        }

        return stack;
      })
      .filter((stack) => stack !== null);

    this.stacks.push(...pointStacks);
    return additionalHeight;
  }

  /**
   * Create a single geophysical point stack
   */
  private createGeophysicalPointStack(
    geophysicName: string,
    index: number,
    currentImageRow: any,
    imageIndex: number,
    lastEndY: number,
    totalRowHeight: number,
    pointHeight: number,
    pointGap: number
  ): LoggingViewStack | null {
    const geophsicDatas = this.dataDownholes
      ?.filter(
        (dataDownhole) =>
          Number(dataDownhole["Depth (m)"]) >= Number(currentImageRow.depthFrom) &&
          Number(dataDownhole["Depth (m)"]) <= Number(currentImageRow.depthTo)
      )
      .map((dataDownhole) => ({
        x: dataDownhole["Depth (m)"],
        y: dataDownhole[geophysicName],
      }));

    // Filter to get unique data, always get latest value
    const uniqueGeophysicData = [
      ...new Map(geophsicDatas.map((item) => [item.x, item])).values(),
    ];
    uniqueGeophysicData.sort((a: any, b: any) => parseFloat(a.x) - parseFloat(b.x));

    if (uniqueGeophysicData.length === 0) return null;

    const resizePoints = resizeLoggingPointData(
      uniqueGeophysicData,
      currentImageRow.depthFrom,
      currentImageRow.depthTo,
      currentImageRow.coordinate.Width,
      pointHeight
    );

    return {
      id: uniqueId(),
      type: EnumLoggingViewStack.Point,
      data: resizePoints,
      index: index,
      startY: lastEndY + this.imageGap + totalRowHeight + pointHeight + pointGap,
    };
  }

  /**
   * Check if overlay stack should be shown for current image index
   */
  private shouldShowOverlay(imageIndex: number): boolean {
    return (
      this.imageExtraRows?.length > 0 &&
      !isEmpty(this.imageExtraRows?.[imageIndex]) &&
      this.extraViews.includes(EnumLoggingExtraViews.Overlay)
    );
  }

  /**
   * Check if below stack should be shown for current image index
   */
  private shouldShowBelow(imageIndex: number): boolean {
    return (
      this.imageExtraRows?.length > 0 &&
      !isEmpty(this.imageExtraRows?.[imageIndex]) &&
      this.extraViews.includes(EnumLoggingExtraViews.Below)
    );
  }

  /**
   * Check if logging bars should be shown
   */
  private shouldShowLoggingBars(): boolean {
    return (
      this.loggingBarData &&
      this.loggingBarData.length > 0 &&
      !!this.selectedImageSubTypeId &&
      this.rockGroupFieldIds.length > 0
    );
  }
}
